import jwt from 'jsonwebtoken';

/**
 * Generate a JWT token for the user
 * @param {String} id - User ID
 * @param {String} email - User email
 * @param {String} role - User role
 * @returns {String} JWT token
 */
export const generateToken = (id, email, role) => {
  const payload = { id, email, role };
  const secret = process.env.JWT_SECRET;

  if (!secret) {
    throw new Error('JWT_SECRET is not defined in environment variables');
  }

  return jwt.sign(payload, secret, {
    expiresIn: process.env.JWT_EXPIRES_IN || '7d', // Default to 7 days if not specified
  });
};

/**
 * Verify a JWT token
 * @param {String} token - JWT token
 * @returns {Object} Decoded payload
 */
export const verifyToken = (token) => {
  const secret = process.env.JWT_SECRET;

  if (!secret) {
    throw new Error('JWT_SECRET is not defined in environment variables');
  }

  try {
    return jwt.verify(token, secret);
  } catch (err) {
    throw new Error('Token is invalid or expired');
  }
};
