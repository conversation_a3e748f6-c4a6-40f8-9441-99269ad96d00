// authMiddleware.js
// import { Clerk } from "@clerk/clerk-sdk-node";
import jwt from "jsonwebtoken";
import User from "../models/user.model.js";

// check for permission based on roles

export const authorize = (roles) => {
  return async (req, res, next) => {
    // console.log("cookies", req.cookies);
    // console.log(req);
    const { userId } = req.auth;

    const user = await User.findOne({ clerkId: userId });

    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // it means if the roles array does not include the user role
    if (!roles.includes(user.role)) {
      return res.status(403).json({ message: "Not authorized" });
    }

    next();
  };
};

//export const requireSession = async (req, res, next) => {
//   try {
//     const { sessionId } = req.headers; // Assuming sessionId is sent in the request headers

//     if (!sessionId) {
//       return res.status(400).json({ message: "Session ID is required" });
//     }

//     // Verify the session using Clerk's SDK
//     const session = await Clerk.sessions.verifySession(sessionId);

//     if (!session) {
//       return res.status(401).json({ message: "Invalid session" });
//     }

//     // Attach session data to request for use in other parts of the app
//     req.session = session;

//     next();
//   } catch (error) {
//     console.error("Session verification error:", error);
//     return res.status(500).json({ message: "Session verification failed" });
//   }
// };

//not required while using clerk as clerk provide requireAuth() function to check if user is authenticated or not

// export const protect = async (req, res, next) => {
//   const token = req.header("Authorization")?.split(" ")[1];

//   if (!token) {
//     return res.status(401).json({ message: "No token, authorization denied" });
//   }

//   try {
//     const decoded = jwt.verify(token, process.env.JWT_SECRET);
//     req.user = await User.findById(decoded.id);
//     next();
//   } catch (err) {
//     res.status(401).json({ message: "Token is not valid" });
//   }
// };
