import User from "../models/user.model.js";
import Product from "../models/product.model.js";
import Access from "../models/access.model.js";
import Subscription from "../models/subscription.model.js";

/**
 * Middleware to check if user has valid subscription
 */
export const requireSubscription = (requiredTiers = []) => {
  return async (req, res, next) => {
    try {
      const { userId } = req.auth;
      
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "Authentication required",
        });
      }

      const user = await User.findOne({ clerkId: userId });
      if (!user) {
        return res.status(404).json({
          success: false,
          message: "User not found",
        });
      }

      // Check if user has active subscription
      if (user.subscriptionStatus !== "active") {
        return res.status(403).json({
          success: false,
          message: "Active subscription required",
          subscriptionStatus: user.subscriptionStatus,
        });
      }

      // Check if user's subscription tier meets requirements
      if (requiredTiers.length > 0 && !requiredTiers.includes(user.subscriptionTier)) {
        return res.status(403).json({
          success: false,
          message: "Higher subscription tier required",
          currentTier: user.subscriptionTier,
          requiredTiers,
        });
      }

      req.user = user;
      next();
    } catch (error) {
      console.error("Subscription middleware error:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
      });
    }
  };
};

/**
 * Middleware to check product access permissions
 */
export const checkProductAccess = async (req, res, next) => {
  try {
    const { userId } = req.auth;
    const { productId, slug } = req.params;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "Authentication required",
      });
    }

    const user = await User.findOne({ clerkId: userId });
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    // Find product by ID or slug
    let product;
    if (productId) {
      product = await Product.findById(productId);
    } else if (slug) {
      product = await Product.findOne({ slug, status: "active" });
    }

    if (!product) {
      return res.status(404).json({
        success: false,
        message: "Product not found",
      });
    }

    // Check if product is free
    if (!product.isPaid || product.accessLevel === "free") {
      req.user = user;
      req.product = product;
      req.hasAccess = true;
      return next();
    }

    // Check user access to paid product
    const hasAccess = user.hasAccessToProduct(product);
    
    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: "Access denied. Subscription required.",
        product: {
          name: product.name,
          accessLevel: product.accessLevel,
          requiredSubscription: product.requiredSubscription,
        },
        userSubscription: {
          tier: user.subscriptionTier,
          status: user.subscriptionStatus,
        },
      });
    }

    req.user = user;
    req.product = product;
    req.hasAccess = true;
    next();
  } catch (error) {
    console.error("Product access middleware error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

/**
 * Middleware to check download limits
 */
export const checkDownloadLimits = async (req, res, next) => {
  try {
    const user = req.user;
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: "User not found in request",
      });
    }

    // Check if user can download
    if (!user.canDownload()) {
      return res.status(429).json({
        success: false,
        message: "Download limit exceeded",
        currentDownloads: user.downloadCount,
        monthlyLimit: user.monthlyDownloadLimit,
        subscriptionTier: user.subscriptionTier,
      });
    }

    next();
  } catch (error) {
    console.error("Download limits middleware error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

/**
 * Middleware to validate subscription status and sync with database
 */
export const syncSubscriptionStatus = async (req, res, next) => {
  try {
    const { userId } = req.auth;
    
    if (!userId) {
      return next();
    }

    const user = await User.findOne({ clerkId: userId });
    if (!user) {
      return next();
    }

    // Get user's active subscription
    const activeSubscription = await Subscription.findOne({
      userId: user._id,
      isActive: true,
      isCurrent: true,
    }).populate("planId");

    if (activeSubscription) {
      // Check if subscription is expired
      if (activeSubscription.endDate && activeSubscription.endDate < new Date()) {
        // Mark subscription as expired
        activeSubscription.isActive = false;
        await activeSubscription.save();
        
        // Update user status
        user.subscriptionStatus = "expired";
        user.subscriptionTier = "free";
        await user.save();
      } else {
        // Sync user subscription data with active subscription
        const plan = activeSubscription.planId;
        user.subscriptionTier = plan.duration;
        user.subscriptionStatus = "active";
        user.subscriptionExpiry = activeSubscription.endDate;
        
        // Set access level based on plan
        if (plan.duration === "lifetime") {
          user.accessLevel = "enterprise";
        } else if (plan.duration === "yearly") {
          user.accessLevel = "premium";
        } else {
          user.accessLevel = "basic";
        }
        
        await user.save();
      }
    } else {
      // No active subscription found
      if (user.subscriptionStatus === "active") {
        user.subscriptionStatus = "inactive";
        user.subscriptionTier = "free";
        user.accessLevel = "basic";
        await user.save();
      }
    }

    req.user = user;
    next();
  } catch (error) {
    console.error("Sync subscription middleware error:", error);
    next(); // Continue even if sync fails
  }
};

export default {
  requireSubscription,
  checkProductAccess,
  checkDownloadLimits,
  syncSubscriptionStatus,
};
