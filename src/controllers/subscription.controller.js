import Subscription from "../models/subscription.model.js";
import Transaction from "../models/transaction.model.js";
import Plan from "../models/plan.model.js";
import User from "../models/user.model.js";
import { asyncHandler } from "../utils/asyncHandler.js";
import { TRANSACTION_TYPES, TRANSACTION_STATUSES } from "../utils/constants.js";

/**
 * Create a new subscription with transaction
 */
export const createSubscription = asyncHandler(async (req, res) => {
  const {
    userId,
    planId,
    startDate,
    endDate,
    isActive = true,
    autoRenew = true,
    transactionId, // Reference to the transaction that created this subscription
  } = req.body;

  // Check if user exists
  const user = await User.findById(userId);
  if (!user) {
    return res.status(404).json({ success: false, message: "User not found" });
  }

  // Check if transaction exists and is completed
  let transaction = null;
  if (transactionId) {
    transaction = await Transaction.findById(transactionId);
    if (!transaction) {
      return res.status(404).json({ success: false, message: "Transaction not found" });
    }
    if (transaction.status !== TRANSACTION_STATUSES.COMPLETED) {
      return res.status(400).json({ success: false, message: "Transaction must be completed to create subscription" });
    }
  }

  const newSubscription = new Subscription({
    userId,
    planId,
    startDate: startDate || Date.now(),
    endDate,
    isActive,
    autoRenew,
    initialTransaction: transactionId,
    lastTransaction: transactionId,
    status: isActive ? "active" : "pending",
  });

  const savedSubscription = await newSubscription.save();

  // Update user's subscriptionPlan
  user.subscriptionPlan = planId;
  await user.save();

  res.status(201).json({ success: true, data: savedSubscription });
});

/**
 * Assign a subscription to a user (admin function)
 */
export const assignSubscription = asyncHandler(async (req, res) => {
  const { userId, planId, transactionId } = req.body;

  const user = await User.findById(userId);
  if (!user) {
    return res.status(404).json({ success: false, message: "User not found" });
  }

  const plan = await Plan.findById(planId);
  if (!plan) {
    return res.status(404).json({ success: false, message: "Plan not found" });
  }

  const activeSubscription = await Subscription.findOne({
    userId,
    isActive: true,
    status: "active",
  });
  if (activeSubscription) {
    return res.status(400).json({
      success: false,
      message: "User already has an active subscription",
    });
  }

  // Calculate end date based on plan duration
  let endDate = null;
  if (plan.duration !== "lifetime") {
    const startDate = new Date();
    endDate = new Date(startDate);
    if (plan.duration === "monthly") {
      endDate.setMonth(endDate.getMonth() + 1);
    } else if (plan.duration === "yearly") {
      endDate.setFullYear(endDate.getFullYear() + 1);
    }
  }

  const newSubscription = new Subscription({
    userId,
    planId,
    startDate: Date.now(),
    endDate,
    isActive: true,
    isCurrent: true,
    autoRenew: plan.duration !== "lifetime",
    status: "active",
    initialTransaction: transactionId,
    lastTransaction: transactionId,
  });

  const savedSubscription = await newSubscription.save();

  // Update the user's subscription plan
  user.subscriptionPlan = planId;
  user.isPro = true; // Optional: Mark the user as Pro if relevant
  await user.save();

  res.status(201).json({ success: true, data: savedSubscription });
});

/**
 * Get all subscriptions
 */
export const getSubscriptions = asyncHandler(async (req, res) => {
  const subscriptions = await Subscription.find()
    .populate("userId", "firstName lastName email")
    .populate("planId", "name price duration")
    .populate("initialTransaction", "amount status createdAt")
    .populate("lastTransaction", "amount status createdAt");

  res.status(200).json({ success: true, data: subscriptions });
});

/**
 * Get a subscription by ID
 */
export const getSubscriptionById = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const subscription = await Subscription.findById(id)
    .populate("userId", "firstName lastName email")
    .populate("planId", "name price");

  if (!subscription) {
    return res
      .status(404)
      .json({ success: false, message: "Subscription not found" });
  }

  res.status(200).json({ success: true, data: subscription });
});

/**
 * Update a subscription by ID
 */
export const updateSubscription = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const updates = req.body;

  const updatedSubscription = await Subscription.findByIdAndUpdate(
    id,
    updates,
    {
      new: true,
    }
  );

  if (!updatedSubscription) {
    return res
      .status(404)
      .json({ success: false, message: "Subscription not found" });
  }

  res.status(200).json({ success: true, data: updatedSubscription });
});

/**
 * Cancel a subscription by ID
 */
export const cancelSubscription = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const canceledSubscription = await Subscription.findByIdAndUpdate(
    id,
    { isActive: false, autoRenew: false },
    { new: true }
  );

  if (!canceledSubscription) {
    return res
      .status(404)
      .json({ success: false, message: "Subscription not found" });
  }

  // Optionally update the user's subscriptionPlan
  const user = await User.findById(canceledSubscription.userId);
  if (user) {
    user.subscriptionPlan = null;
    user.isPro = false;
    await user.save();
  }

  res.status(200).json({ success: true, data: canceledSubscription });
});

/**
 * Delete a subscription by ID (Hard delete)
 */
export const deleteSubscription = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const deletedSubscription = await Subscription.findByIdAndDelete(id);

  if (!deletedSubscription) {
    return res
      .status(404)
      .json({ success: false, message: "Subscription not found" });
  }

  res.status(200).json({
    success: true,
    message: "Subscription deleted successfully",
  });
});
