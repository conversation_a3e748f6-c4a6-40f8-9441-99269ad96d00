import User from "../models/user.model.js";
import bcrypt from "bcryptjs";
import { generateToken } from "../utils/jwt.js"; // Utility for generating JWT token
import Subscription from "../models/subscription.model.js"; // To manage subscription data
import { asyncHandler } from "../utils/asyncHandler.js";

// Create User
export const createUser = async (req, res) => {
  try {
    const {
      email,
      password,
      firstName,
      lastName,
      profileImage,
      role,
      clerkId,
      googleId,
    } = req.body;

    // Check if user exists
    const userExists = await User.findOne({ email });
    if (userExists) {
      return res.status(400).json({ message: "User already exists" });
    }

    // Hash password if provided
    let hashedPassword = null;
    if (password) {
      hashedPassword = await bcrypt.hash(password, 12);
    }

    // Create new user
    const newUser = new User({
      email,
      password: hashedPassword,
      firstName,
      lastName,
      profileImage,
      role,
      clerkId,
      googleId,
    });

    const savedUser = await newUser.save();

    res.status(201).json({ user: savedUser });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Error creating user" });
  }
};

// Get All Users
export const getUsers = async (req, res) => {
  try {
    const users = await User.find({ isDeleted: false });
    // .populate(
    //   "activeSubscription"
    // );
    res.status(200).json({ users });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Error fetching users" });
  }
};

// Get User with pagination
export const getUsersWithPagination = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const skip = (page - 1) * limit;
  const total = await User.countDocuments();
  const pages = Math.ceil(total / limit);
  const users = await User.find({ isDeleted: false })
    // .populate("activeSubscription")
    .skip(skip)
    .limit(limit);

  res.status(200).json({
    data: users,
    totalDocuments: total,
    currentPage: page,
    totalPages: pages,
  });
});

// Get Single User
export const getUserById = async (req, res) => {
  try {
    const user = await User.findById(req.params.id);
    // .populate(
    //   "activeSubscription"
    // );
    console.log(user);
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }
    res.status(200).json({ user });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Error fetching user" });
  }
};

// Update User
export const updateUser = async (req, res) => {
  try {
    const { email, password, ...updateData } = req.body;

    if (password) {
      updateData.password = await bcrypt.hash(password, 12); // Re-hash new password if provided
    }

    const updatedUser = await User.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true }
    );
    if (!updatedUser) {
      return res.status(404).json({ message: "User not found" });
    }
    res.status(200).json({ user: updatedUser });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Error updating user" });
  }
};

// Delete User (Soft Delete)
export const deleteUser = async (req, res) => {
  try {
    const deletedUser = await User.findByIdAndUpdate(
      req.params.id,
      { isDeleted: true },
      { new: true }
    );
    if (!deletedUser) {
      return res.status(404).json({ message: "User not found" });
    }
    res.status(200).json({ message: "User deleted successfully" });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Error deleting user" });
  }
};

// User Login
export const loginUser = async (req, res) => {
  try {
    const { email, password } = req.body;

    const user = await User.findOne({ email });
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // Check password
    const isMatch = await bcrypt.compare(password, user.password || "");
    if (!isMatch) {
      return res.status(400).json({ message: "Invalid credentials" });
    }

    // Generate token
    const token = generateToken(user._id, user.email, user.role);

    res.status(200).json({ message: "Login successful", token });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Error logging in" });
  }
};

// Assign Subscription to User
export const assignSubscription = async (req, res) => {
  try {
    const { userId, planId, paymentMethod } = req.body;

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // Deactivate existing subscriptions
    await Subscription.updateMany(
      { userId, isCurrent: true },
      { isCurrent: false }
    );

    // Create new subscription
    const newSubscription = new Subscription({
      userId,
      planId,
      startDate: new Date(),
      isCurrent: true,
      isActive: true,
      paymentMethod,
      paymentStatus: "paid",
    });

    await newSubscription.save();

    res.status(201).json({ message: "Subscription assigned successfully" });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Error assigning subscription" });
  }
};
