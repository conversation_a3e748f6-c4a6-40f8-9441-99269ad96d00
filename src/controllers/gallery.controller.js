import Gallery from "../models/gallery.model.js";
import { asyncHandler } from "../utils/asyncHandler.js";

// Create a new gallery entry
export const createGalleryItem = asyncHandler(async (req, res) => {
  const {
    imageUrl,
    imageKey,
    title,
    description,
    tags,
    isPublic,
    isFeatured,
    fileName,
    fileSize,
    fileType,
    category,
    source,
    sourceDetails,
    uploadedBy,
  } = req.body;

  const newItem = new Gallery({
    imageUrl,
    imageKey,
    title,
    description,
    tags,
    isPublic,
    isFeatured,
    fileName,
    fileSize,
    fileType,
    category,
    source,
    sourceDetails,
    uploadedBy,
  });

  const savedItem = await newItem.save();
  res.status(201).json({
    success: true,
    message: "Gallery item created successfully.",
    item: savedItem,
  });
});

// Fetch all gallery items (no search, no filters)
export const getAllGalleryItemsNotDeleted = asyncHandler(async (req, res) => {
  const galleryItems = await Gallery.find({ isDeleted: false }).sort({
    createdAt: -1,
  });
  res.status(200).json({ success: true, data: galleryItems });
});

// Fetch gallery items with pagination only
export const getGalleryItemsWithPagination = asyncHandler(async (req, res) => {
  const { page = 1, limit = 10 } = req.query; // Default to page 1 and limit 10
  const skip = (page - 1) * limit; // Calculate the skip value for pagination

  const galleryItems = await Gallery.find({ isDeleted: false })
    .skip(skip)
    .limit(limit)
    .sort({ createdAt: -1 });

  const total = await Gallery.countDocuments({ isDeleted: false });

  const pages = Math.ceil(total / limit);

  res.status(200).json({
    success: true,
    data: galleryItems,
    totalDocuments: total,
    totalPages: pages,
    currentPage: page,
  });
});

// Fetch gallery items with pagination and optional search filters
export const getGalleryItemsWithPaginationandSearch = asyncHandler(
  async (req, res) => {
    const { page = 1, limit = 10, search = "" } = req.query;
    const skip = (page - 1) * limit;

    const searchFilter = search
      ? {
          $or: [
            { title: { $regex: search, $options: "i" } },
            { description: { $regex: search, $options: "i" } },
            { tags: { $regex: search, $options: "i" } },
            { category: { $regex: search, $options: "i" } },
          ],
        }
      : {};

    const galleryItems = await Gallery.find({
      isDeleted: false,
      ...searchFilter,
    })
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 });

    const total = await Gallery.countDocuments({
      isDeleted: false,
      ...searchFilter,
    });

    const pages = Math.ceil(totalItems / limit);

    res.status(200).json({
      success: true,
      data: galleryItems,
      totalDocuments: total,
      totalPages: pages,
      currentPage: page,
    });
  }
);

// Fetch a single gallery item by ID
export const getGalleryItemById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const item = await Gallery.findById(id);
  if (!item || item.isDeleted) {
    return res.status(404).json({ error: "Gallery item not found." });
  }

  res.status(200).json({ success: true, data: item });
});

// Update an existing gallery item
export const updateGalleryItem = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const updateData = req.body;

  const updatedItem = await Gallery.findByIdAndUpdate(id, updateData, {
    new: true,
  });

  if (!updatedItem) {
    return res.status(404).json({ error: "Gallery item not found." });
  }

  res.status(200).json({ success: true, data: updatedItem });
});

// Soft delete multiple gallery items by their IDs
export const deleteGalleryItems = asyncHandler(async (req, res) => {
  const { ids } = req.body;

  if (!Array.isArray(ids) || ids.length === 0) {
    return res
      .status(400)
      .json({ error: "Please provide an array of IDs to delete." });
  }

  const deletedItems = await Gallery.updateMany(
    { _id: { $in: ids } },
    { isDeleted: true },
    { new: true }
  );

  if (deletedItems.nModified === 0) {
    return res.status(404).json({ error: "No gallery items found to delete." });
  }

  res.status(200).json({
    success: true,
    message: `${deletedItems.nModified} gallery item(s) deleted successfully.`,
  });
});

// Permanently delete multiple gallery items by their IDs
export const permanentDeleteGalleryItems = asyncHandler(async (req, res) => {
  const { ids } = req.body;

  if (!Array.isArray(ids) || ids.length === 0) {
    return res
      .status(400)
      .json({ error: "Please provide an array of IDs to delete." });
  }

  const deletedItems = await Gallery.deleteMany({ _id: { $in: ids } });

  if (deletedItems.deletedCount === 0) {
    return res.status(404).json({ error: "No gallery items found to delete." });
  }

  res.status(200).json({
    success: true,
    message: `${deletedItems.deletedCount} gallery item(s) permanently deleted.`,
  });
});

// Mark multiple gallery items as featured
export const featureGalleryItems = asyncHandler(async (req, res) => {
  const { ids } = req.body;

  if (!Array.isArray(ids) || ids.length === 0) {
    return res
      .status(400)
      .json({ error: "Please provide an array of IDs to feature." });
  }

  const featuredItems = await Gallery.updateMany(
    { _id: { $in: ids } },
    { isFeatured: true },
    { new: true }
  );

  if (featuredItems.nModified === 0) {
    return res
      .status(404)
      .json({ error: "No gallery items found to feature." });
  }

  res.status(200).json({
    success: true,
    message: `${featuredItems.nModified} gallery item(s) featured successfully.`,
  });
});

// Restore multiple soft-deleted gallery items by their IDs
export const restoreGalleryItems = asyncHandler(async (req, res) => {
  const { ids } = req.body;

  if (!Array.isArray(ids) || ids.length === 0) {
    return res
      .status(400)
      .json({ error: "Please provide an array of IDs to restore." });
  }

  const restoredItems = await Gallery.updateMany(
    { _id: { $in: ids } },
    { isDeleted: false },
    { new: true }
  );

  if (restoredItems.nModified === 0) {
    return res
      .status(404)
      .json({ error: "No gallery items found to restore." });
  }

  res.status(200).json({
    success: true,
    message: `${restoredItems.nModified} gallery item(s) restored successfully.`,
  });
});
