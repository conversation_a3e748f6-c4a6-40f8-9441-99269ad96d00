import User from "../models/user.model.js";
import { asyncHand<PERSON> } from "../utils/asyncHandler.js";
import { Webhook } from "svix";
import logger from "../utils/logger.js";
import { ROLES } from "../utils/constants.js";

// Create user using Clerk webhook
export const clerkWebhook = asyncHandler(async (req, res) => {
  const payloadString = JSON.stringify(req.body);
  const {
    "svix-id": svixId,
    "svix-timestamp": svixTimestamp,
    "svix-signature": svixSignature,
  } = req.headers;

  if (!svixId || !svixTimestamp || !svixSignature) {
    return res
      .status(400)
      .json({ success: false, message: "Missing SVIX headers" });
  }

  try {
    const webhook = new Webhook(process.env.CLERK_WEBHOOK_SECRET_KEY);
    const event = webhook.verify(payloadString, {
      "svix-id": svixId,
      "svix-timestamp": svixTimestamp,
      "svix-signature": svixSignature,
    });
    const {
      id,
      email_addresses,
      image_url,
      profile_image_url,
      first_name,
      last_name,
      username,
      external_accounts,
    } = event.data;
    const eventType = event.type;
    const primaryEmail = email_addresses[0]?.email_address;
    const profileImage = image_url ?? profile_image_url;

    // console.log("external_accounts", external_accounts);
    // const googleAccount = external_accounts?.find(
    //   (account) => account.object === "google_account"
    // );

    const googleAccount = external_accounts?.find(
      (account) => account.verification.strategy === "oauth_google"
    );

    // console.log("googleAccount", googleAccount);
    const googleId =
      googleAccount?.google_id ?? googleAccount?.provider_user_id;

    const updateUser = async (user) => {
      user.email = primaryEmail;
      user.firstName = first_name;
      user.lastName = last_name;
      user.username = username;
      user.profileImage = profileImage;
      if (googleId) user.googleId = googleId;
      await user.save();
    };

    let user = await User.findOne({ clerkId: id });

    switch (eventType) {
      case "user.created":
        if (user) {
          logger.warn("User already exists in database");
          await updateUser(user);
        } else {
          user = new User({
            clerkId: id,
            email: primaryEmail,
            firstName: first_name,
            lastName: last_name,
            username,
            profileImage,
            googleId,
          });
          await user.save();
          logger.info("User saved to database");
        }
        break;
      case "user.updated":
        if (user) {
          await updateUser(user);
          logger.info("User updated in database through update event");
        } else {
          user = new User({
            clerkId: id,
            email: primaryEmail,
            firstName: first_name,
            lastName: last_name,
            username,
            profileImage,
            googleId,
          });
          await user.save();
          logger.info("New user saved to database through update event");
        }
        break;
      case "user.deleted":
        if (user) {
          user.isDeleted = true;
          await user.save();
          logger.info("User soft deleted in database");
        } else {
          logger.warn("User not found for deletion");
        }
        break;
      default:
        logger.warn(`Unhandled event type: ${eventType}`);
    }

    res.status(200).json({ success: true, message: "Webhook received" });
  } catch (err) {
    logger.error(err.message);
    res.status(400).json({ success: false, message: err.message });
  }
});

export const getUserByClerkId = asyncHandler(async (req, res) => {
  // console.log("req.params.clerkId", req.params.id);
  const user = await User.findOne({ clerkId: req.params.id });
  if (!user) {
    return res.status(404).json({ message: "User not found" });
  }
  res.status(200).json({
    success: true,
    data: user,
  });
});


// fetch user access to dashboard or not based on roles

export const getDashboarAccess = asyncHandler(async (req, res) => {
  // console.log("req.params.clerkId", req.params.id);
  const user = await User.findOne({ clerkId: req.params.id });
  if (!user) {
    return res.status(404).json({ message: "User not found" });
  }
  const { role } = user;
  const access = role === ROLES.ADMIN || role === ROLES.SUPER_ADMIN;
  res.status(200).json({
    success: true,
    access,
  });
});
