import { asyncHand<PERSON> } from "../utils/asyncHandler.js";
import { slugGenerator } from "../utils/slug-generator.js";
import Blog from "../models/blog.model.js";

// Create a new blog
export const createBlog = asyncHandler(async (req, res) => {
  const {
    title,
    slug,
    content,
    author,
    categories,
    tags,
    thumbnailUrl,
    isPublished,
    publishDate,
  } = req.body;

  const genratedSlug = slug ? slugGenerator(slug) : slugGenerator(title);

  const newBlog = new Blog({
    title,
    slug: genratedSlug,
    content,
    author,
    categories,
    tags,
    thumbnailUrl,
    isPublished,
    publishDate: isPublished ? publishDate || new Date() : null,
  });

  const savedBlog = await newBlog.save();
  res.status(201).json({ success: true, data: savedBlog });
});

// Get all blogs
export const getAllBlogs = asyncHandler(async (req, res) => {
  const { isPublished, author } = req.query;
  const filter = {};

  if (isPublished !== undefined) filter.isPublished = isPublished === "true";
  if (author) filter.author = author;

  const blogs = await Blog.find(filter).sort({ createdAt: -1 });
  res.status(200).json({ success: true, data: blogs });
});

// Get all blogs with pagination
export const getBlogsWithPagination = asyncHandler(async (req, res) => {
  const { page = 1, limit = 1 } = req.query;
  const skip = (page - 1) * limit;
  const total = await Blog.countDocuments();
  const pages = Math.ceil(total / limit);
  const blogs = await Blog.find()
    .skip(skip)
    .limit(limit)
    .sort({ createdAt: -1 });

  res.status(200).json({
    success: true,
    data: blogs,
    totalDocuments: total,
    currentpage: page,
    totalPages: pages,
  });
});

// Get a blog by ID
export const getBlogById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const blog = await Blog.findById(id);
  if (!blog) {
    res.status(404);
    throw new Error("Blog not found");
  }

  res.status(200).json({ success: true, data: blog });
});

// Update a blog by ID
export const updateBlog = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const updateData = req.body;

  if (updateData.isPublished && !updateData.publishDate) {
    updateData.publishDate = new Date();
  }

  const updatedBlog = await Blog.findByIdAndUpdate(id, updateData, {
    new: true,
    runValidators: true,
  });

  if (!updatedBlog) {
    res.status(404);
    throw new Error("Blog not found");
  }

  res.status(200).json({ success: true, data: updatedBlog });
});

// Delete a blog by ID
export const deleteBlog = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const deletedBlog = await Blog.findByIdAndDelete(id);
  if (!deletedBlog) {
    res.status(404);
    throw new Error("Blog not found");
  }

  res.status(200).json({ success: true, message: "Blog deleted successfully" });
});
