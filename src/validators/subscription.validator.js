import { check, validationResult } from "express-validator";

export const validateCreateSubscription = [
  check("userId")
    .notEmpty()
    .withMessage("User ID is required")
    .isMongoId()
    .withMessage("Invalid User ID format"),
  check("planId")
    .notEmpty()
    .withMessage("Plan ID is required")
    .isMongoId()
    .withMessage("Invalid Plan ID format"),
  check("startDate")
    .notEmpty()
    .withMessage("Start date is required")
    .isISO8601()
    .withMessage("Invalid date format for Start date"),
  check("endDate")
    .optional()
    .isISO8601()
    .withMessage("Invalid date format for End date"),
  check("isActive")
    .optional()
    .isBoolean()
    .withMessage("isActive must be a boolean value"),
  check("autoRenew")
    .optional()
    .isBoolean()
    .withMessage("autoRenew must be a boolean value"),
  check("transactionId")
    .optional()
    .isMongoId()
    .withMessage("Invalid transaction ID format"),
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  },
];

export const validateAssignSubscription = [
  check("userId")
    .notEmpty()
    .withMessage("User ID is required")
    .isMongoId()
    .withMessage("Invalid User ID format"),
  check("planId")
    .notEmpty()
    .withMessage("Plan ID is required")
    .isMongoId()
    .withMessage("Invalid Plan ID format"),
  check("startDate")
    .optional()
    .isISO8601()
    .withMessage("Invalid date format for Start date"),
  check("transactionId")
    .optional()
    .isMongoId()
    .withMessage("Invalid transaction ID format"),
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  },
];

export const validateUpdateSubscription = [
  check("isActive")
    .optional()
    .isBoolean()
    .withMessage("isActive must be a boolean value"),
  check("autoRenew")
    .optional()
    .isBoolean()
    .withMessage("autoRenew must be a boolean value"),
  check("status")
    .optional()
    .isIn(["active", "inactive", "cancelled", "expired", "pending"])
    .withMessage("Invalid subscription status"),
  check("endDate")
    .optional()
    .isISO8601()
    .withMessage("Invalid date format for End date"),
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  },
];

export const validateSubscriptionId = [
  check("id").notEmpty().isMongoId().withMessage("Invalid subscription ID"),
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  },
];
