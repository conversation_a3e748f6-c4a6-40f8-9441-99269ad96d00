import { body, param, query } from "express-validator";

export const createBlogValidator = [
  body("title")
    .isString()
    .notEmpty()
    .withMessage("Title is required.")
    .isLength({ min: 3 })
    .withMessage("Title must be at least 3 characters long."),
  body("slug")
    .isString()
    .notEmpty()
    .withMessage("Slug is required.")
    .isSlug()
    .withMessage("Slug must be a valid slug."),
  body("content").isString().notEmpty().withMessage("Content is required."),
  body("author").isMongoId().withMessage("Author must be a valid MongoDB ID."),
  body("categories")
    .optional()
    .isArray()
    .withMessage("Categories must be an array of strings.")
    .custom((categories) => categories.every((cat) => typeof cat === "string"))
    .withMessage("Each category must be a string."),
  body("tags")
    .optional()
    .isArray()
    .withMessage("Tags must be an array of strings.")
    .custom((tags) => tags.every((tag) => typeof tag === "string"))
    .withMessage("Each tag must be a string."),
  body("thumbnailUrl")
    .optional()
    .isURL()
    .withMessage("Thumbnail URL must be a valid URL."),
  body("isPublished")
    .optional()
    .isBoolean()
    .withMessage("isPublished must be a boolean value."),
  body("publishDate")
    .optional()
    .isISO8601()
    .withMessage("Publish date must be a valid ISO 8601 date."),
];

export const updateBlogValidator = [
  param("id").isMongoId().withMessage("Invalid blog ID."),
  body("title")
    .optional()
    .isString()
    .isLength({ min: 3 })
    .withMessage("Title must be at least 3 characters long."),
  body("slug")
    .optional()
    .isString()
    .isSlug()
    .withMessage("Slug must be a valid slug."),
  body("content")
    .optional()
    .isString()
    .notEmpty()
    .withMessage("Content cannot be empty if provided."),
  body("author")
    .optional()
    .isMongoId()
    .withMessage("Author must be a valid MongoDB ID."),
  body("categories")
    .optional()
    .isArray()
    .withMessage("Categories must be an array of strings.")
    .custom((categories) => categories.every((cat) => typeof cat === "string"))
    .withMessage("Each category must be a string."),
  body("tags")
    .optional()
    .isArray()
    .withMessage("Tags must be an array of strings.")
    .custom((tags) => tags.every((tag) => typeof tag === "string"))
    .withMessage("Each tag must be a string."),
  body("thumbnailUrl")
    .optional()
    .isURL()
    .withMessage("Thumbnail URL must be a valid URL."),
  body("isPublished")
    .optional()
    .isBoolean()
    .withMessage("isPublished must be a boolean value."),
  body("publishDate")
    .optional()
    .isISO8601()
    .withMessage("Publish date must be a valid ISO 8601 date."),
];

export const getBlogByIdValidator = [
  param("id").isMongoId().withMessage("Invalid blog ID."),
];

export const deleteBlogValidator = [
  param("id").isMongoId().withMessage("Invalid blog ID."),
];

export const getBlogsQueryValidator = [
  query("isPublished")
    .optional()
    .isBoolean()
    .withMessage("isPublished must be a boolean value."),
  query("author")
    .optional()
    .isMongoId()
    .withMessage("Author must be a valid MongoDB ID."),
];
