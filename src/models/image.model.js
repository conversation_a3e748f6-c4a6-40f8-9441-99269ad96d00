const mongoose = require("mongoose");

const imageSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    folders: [
      {
        folder: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Folder",
          required: true,
        },
        addedAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
    originalFolder: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Folder",
      required: true,
      immutable: true, // Can't change original folder
    },
    path: {
      type: String,
      required: true,
      unique: true,
    },
    metadata: {
      size: Number,
      mimeType: String,
      dimensions: {
        width: Number,
        height: Number,
      },
      takenAt: Date, // From EXIF data if available
    },
    tags: [
      {
        type: String,
        trim: true,
      },
    ],
  },
  {
    timestamps: true,
  }
);

// Indexes for better query performance
imageSchema.index({ "folders.folder": 1 });
imageSchema.index({ originalFolder: 1 });
imageSchema.index({ path: 1 }, { unique: true });
imageSchema.index({ tags: 1 });
imageSchema.index({ "metadata.takenAt": 1 });

// Virtual for folder count
imageSchema.virtual("folderCount").get(function () {
  return this.folders.length;
});

// Middleware to ensure originalFolder is also in folders array
imageSchema.pre("save", function (next) {
  if (this.isNew) {
    const hasOriginalFolder = this.folders.some(
      (f) => f.folder.toString() === this.originalFolder.toString()
    );

    if (!hasOriginalFolder) {
      this.folders.push({
        folder: this.originalFolder,
        addedAt: this.createdAt,
      });
    }
  }
  next();
});

module.exports = mongoose.model("Image", imageSchema);
