import mongoose from "mongoose";

const folderSchema = new mongoose.Schema(
  {
    name: { type: String, required: true }, // Folder name
    parent: { type: mongoose.Schema.Types.ObjectId, ref: "Folder" }, // Reference to the parent folder (null for root folders)
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: "User" }, // User who created the folder
    isDeleted: { type: Boolean, default: false }, // For soft delete
    path: {
      type: String,
      required: true,
    },
  },
  { timestamps: true }
);

// Add an index for quick searches within folders
folderSchema.index({ name: 1, parentFolder: 1 });

const Folder = mongoose.model("Folder", folderSchema);

export default Folder;
