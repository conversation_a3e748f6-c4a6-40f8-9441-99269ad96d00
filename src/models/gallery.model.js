import mongoose from "mongoose";

const gallerySchema = new mongoose.Schema(
  {
    imageUrl: { type: String, required: true }, // URL or path to the image
    imageKey: { type: String }, // Key/ID for S3 or similar storage

    title: { type: String, required: true }, // Image title
    description: { type: String }, // Optional description

    // tags: { type: [String] }, // Tags for filtering/categorization
    tags: [{ type: String }], // above declration is similar to this array or string

    isPublic: { type: Boolean, default: true }, // Visibility of the image
    isDeleted: { type: Boolean, default: false }, // For soft delete
    isFeatured: { type: <PERSON>olean, default: false }, // To highlight featured images

    fileName: { type: String }, // Original file name
    fileSize: { type: Number }, // File size in bytes
    fileType: { type: String }, // MIME type of the file
    category: { type: String }, // Category of the image (e.g., nature, portraits, etc.)

    source: {
      type: String,
      enum: ["S3", "UploadThing", "Unsplash", "External"],
      required: true,
    }, // Source of the image
    sourceDetails: { type: String }, // Additional details about the source, like an external URL or metadata

    uploadedBy: { type: mongoose.Schema.Types.ObjectId, ref: "User" }, // User who uploaded the image
    folder: { type: mongoose.Schema.Types.ObjectId, ref: "Folder" }, // Reference to the folder the image belongs to
  },
  { timestamps: true }
);

// Create a compound text index for searching by `title`, `description`, `tags`, and `category`
gallerySchema.index(
  { title: "text", description: "text", tags: "text", category: "text" },
  {
    weights: {
      title: 5, // Title has the highest weight
      description: 3, // Description is moderately important
      tags: 2, // Tags have lower weight
      category: 1, // Category has the lowest weight
    },
    name: "TextSearchIndex",
  }
);

// Add additional indexes for common queries
// gallerySchema.index({ isPublic: 1, tags: 1, createdAt: -1 }); // For filtering by visibility, tags, or time
// gallerySchema.index({ isDeleted: 1 }); // To quickly filter out deleted images
// gallerySchema.index({ isFeatured: 1 }); // To fetch featured images quickly
// gallerySchema.index({ category: 1, createdAt: -1 }); // For category-based queries and sorting

// Create indexes for folder-based queries
// gallerySchema.index({ folder: 1, isPublic: 1, createdAt: -1 });



const Gallery = mongoose.model("Gallery", gallerySchema);

export default Gallery;
