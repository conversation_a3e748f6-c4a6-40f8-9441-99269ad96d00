import mongoose from "mongoose";
import { ROLES } from "../utils/constants.js";

const userSchema = new mongoose.Schema(
  {
    // Authentication Details
    clerkId: { type: String, unique: true },
    email: { type: String, required: true, unique: true },
    username: { type: String, unique: true, required: true },
    password: { type: String },
    googleId: { type: String, unique: true },
    provider: {
      type: String,
      enum: ["clerk", "google", "email"],
      default: "clerk",
    },

    // Profile Details
    firstName: { type: String },
    lastName: { type: String },
    profileImage: { type: String },
    phoneNumber: { type: String },
    address: {
      street: { type: String },
      city: { type: String },
      state: { type: String },
      postalCode: { type: String },
      country: { type: String },
    },

    // Role and Access
    role: { type: String, enum: Object.values(ROLES), default: ROLES.USER },
    isDeleted: { type: Boolean, default: false },

    // Status and Metadata
    lastLogin: { type: Date },
    createdBy: { type: String, enum: ["self", "admin"], default: "self" },

 

// Subscription and Access Control
    // Core subscription status
    isPro: { type: Boolean, default: false },
    isLifetimePro: { type: Boolean, default: false },

    // Subscription details
    subscriptionTier: {
      type: String,
      enum: ["free", "monthly", "yearly", "lifetime"],
      default: "free",
    },
    subscriptionStatus: {
      type: String,
      enum: ["active", "inactive", "expired", "cancelled", "pending"],
      default: "inactive",
    },

    // Subscription dates
    subscriptionStartDate: { type: Date },
    subscriptionEndDate: { type: Date },
    subscriptionExpiry: { type: Date }, // Keeping for backward compatibility

    // Current plan reference
    currentPlan: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Plan"
    },

    // Plan details snapshot (for historical reference)
    currentPlanDetails: {
      name: { type: String },
      price: { type: Number },
      duration: { type: String },
      benefits: { type: String }
    },

    // Renewal and cancellation
    autoRenew: { type: Boolean, default: true },
    nextRenewalDate: { type: Date },
    cancellation: {
      cancelledAt: { type: Date },
      cancelledBy: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
      reason: { type: String },
      effectiveDate: { type: Date }
    },

    // Trial information
    trial: {
      isTrialActive: { type: Boolean, default: false },
      trialStartDate: { type: Date },
      trialEndDate: { type: Date },
      hasUsedTrial: { type: Boolean, default: false }
    },

    // Access levels and limits
    accessLevel: {
      type: String,
      enum: ["basic", "premium", "enterprise"],
      default: "basic",
    },
    downloadCount: { type: Number, default: 0 },
    monthlyDownloadLimit: { type: Number, default: 5 }, // For free users

    // Transaction references
    lastTransaction: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Transaction"
    },

    // Subscription metadata
    subscriptionMetadata: {
      source: { type: String }, // web, mobile, etc.
      campaignId: { type: String },
      affiliateId: { type: String },
      paymentMethod: { type: String },
      customData: { type: mongoose.Schema.Types.Mixed }
    }
  },
  { timestamps: true }
);

// Virtual field to fetch the current active subscription
userSchema.virtual("activeSubscription", {
  ref: "Subscription",
  localField: "_id",
  foreignField: "userId",
  justOne: true, // Ensure it only returns the latest subscription
  options: { sort: { endDate: -1 }, match: { isActive: true } },
});

// Method to check if user has access to a product
userSchema.methods.hasAccessToProduct = function(product) {
  // Free products are accessible to everyone
  if (!product.isPaid) return true;

  // Check subscription status
  if (this.subscriptionStatus !== "active") return false;

  // Check access level requirements
  const accessLevels = {
    basic: ["free"],
    premium: ["free", "monthly", "yearly"],
    enterprise: ["free", "monthly", "yearly", "lifetime"]
  };

  const requiredTiers = accessLevels[product.accessLevel] || ["free"];
  return requiredTiers.includes(this.subscriptionTier);
};

// Subscription Management Methods

// Check if user has active subscription
userSchema.methods.hasActiveSubscription = function() {
  return this.isPro &&
         this.subscriptionStatus === "active" &&
         (!this.subscriptionEndDate || this.subscriptionEndDate > new Date());
};

// Check if user is lifetime pro
userSchema.methods.isLifetime = function() {
  return this.isLifetimePro && this.subscriptionTier === "lifetime";
};

// Check if subscription is expired
userSchema.methods.isSubscriptionExpired = function() {
  if (this.isLifetimePro) return false;
  if (!this.subscriptionEndDate) return true;
  return this.subscriptionEndDate < new Date();
};

// Activate subscription
userSchema.methods.activateSubscription = function(plan, transaction, startDate = new Date()) {
  this.isPro = true;
  this.subscriptionTier = plan.duration;
  this.subscriptionStatus = "active";
  this.subscriptionStartDate = startDate;
  this.currentPlan = plan._id;
  this.lastTransaction = transaction._id;

  // Set plan details snapshot
  this.currentPlanDetails = {
    name: plan.name,
    price: plan.price,
    duration: plan.duration,
    benefits: plan.benefits
  };

  // Handle lifetime subscription
  if (plan.duration === "lifetime") {
    this.isLifetimePro = true;
    this.subscriptionEndDate = null;
    this.nextRenewalDate = null;
    this.autoRenew = false;
  } else {
    // Calculate end date based on duration
    const endDate = new Date(startDate);
    if (plan.duration === "monthly") {
      endDate.setMonth(endDate.getMonth() + 1);
    } else if (plan.duration === "yearly") {
      endDate.setFullYear(endDate.getFullYear() + 1);
    }

    this.subscriptionEndDate = endDate;
    this.subscriptionExpiry = endDate; // For backward compatibility

    if (this.autoRenew) {
      this.nextRenewalDate = endDate;
    }
  }

  return this.save();
};

// Cancel subscription
userSchema.methods.cancelSubscription = function(cancelledBy, reason, effectiveDate = null) {
  this.cancellation = {
    cancelledAt: new Date(),
    cancelledBy,
    reason,
    effectiveDate: effectiveDate || this.subscriptionEndDate || new Date()
  };

  // If effective immediately or lifetime, update status
  if (!effectiveDate || effectiveDate <= new Date() || this.isLifetimePro) {
    this.subscriptionStatus = "cancelled";
    this.isPro = false;
    this.autoRenew = false;
    this.nextRenewalDate = null;
  }

  return this.save();
};

// Renew subscription
userSchema.methods.renewSubscription = function(plan, transaction) {
  if (this.isLifetimePro) {
    throw new Error("Lifetime subscriptions cannot be renewed");
  }

  const currentEndDate = this.subscriptionEndDate || new Date();
  const newEndDate = new Date(currentEndDate);

  if (plan.duration === "monthly") {
    newEndDate.setMonth(newEndDate.getMonth() + 1);
  } else if (plan.duration === "yearly") {
    newEndDate.setFullYear(newEndDate.getFullYear() + 1);
  }

  this.subscriptionEndDate = newEndDate;
  this.subscriptionExpiry = newEndDate; // For backward compatibility
  this.subscriptionStatus = "active";
  this.isPro = true;
  this.lastTransaction = transaction._id;

  if (this.autoRenew) {
    this.nextRenewalDate = newEndDate;
  }

  return this.save();
};

// Upgrade/downgrade subscription
userSchema.methods.changeSubscription = function(newPlan, transaction) {
  this.currentPlan = newPlan._id;
  this.subscriptionTier = newPlan.duration;
  this.lastTransaction = transaction._id;

  // Update plan details
  this.currentPlanDetails = {
    name: newPlan.name,
    price: newPlan.price,
    duration: newPlan.duration,
    benefits: newPlan.benefits
  };

  // Handle upgrade to lifetime
  if (newPlan.duration === "lifetime") {
    this.isLifetimePro = true;
    this.subscriptionEndDate = null;
    this.nextRenewalDate = null;
    this.autoRenew = false;
  }

  return this.save();
};

// Method to check download limits
userSchema.methods.canDownload = function() {
  if (this.hasActiveSubscription() || this.isLifetime()) {
    return true; // Pro users have unlimited downloads
  }
  return this.downloadCount < this.monthlyDownloadLimit;
};

// Method to increment download count
userSchema.methods.incrementDownload = function() {
  this.downloadCount += 1;
  return this.save();
};

// Reset monthly download count (to be called monthly for free users)
userSchema.methods.resetMonthlyDownloads = function() {
  if (this.subscriptionTier === "free") {
    this.downloadCount = 0;
    return this.save();
  }
};

// Get subscription summary
userSchema.methods.getSubscriptionSummary = function() {
  return {
    isPro: this.isPro,
    isLifetime: this.isLifetimePro,
    tier: this.subscriptionTier,
    status: this.subscriptionStatus,
    startDate: this.subscriptionStartDate,
    endDate: this.subscriptionEndDate,
    autoRenew: this.autoRenew,
    nextRenewalDate: this.nextRenewalDate,
    hasActiveSubscription: this.hasActiveSubscription(),
    isExpired: this.isSubscriptionExpired(),
    currentPlan: this.currentPlanDetails,
    trial: this.trial,
    cancellation: this.cancellation
  };
};

export default mongoose.model("User", userSchema);
