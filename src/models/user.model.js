import mongoose from "mongoose";
import { ROLES } from "../utils/constants.js";

const userSchema = new mongoose.Schema(
  {
    // Authentication Details
    clerkId: { type: String, unique: true },
    email: { type: String, required: true, unique: true },
    username: { type: String, unique: true, required: true },
    password: { type: String },
    googleId: { type: String, unique: true },
    provider: {
      type: String,
      enum: ["clerk", "google", "email"],
      default: "clerk",
    },

    // Profile Details
    firstName: { type: String },
    lastName: { type: String },
    profileImage: { type: String },
    phoneNumber: { type: String },
    address: {
      street: { type: String },
      city: { type: String },
      state: { type: String },
      postalCode: { type: String },
      country: { type: String },
    },

    // Role and Access
    role: { type: String, enum: Object.values(ROLES), default: ROLES.USER },
    isDeleted: { type: Boolean, default: false },

    // Status and Metadata
    lastLogin: { type: Date },
    createdBy: { type: String, enum: ["self", "admin"], default: "self" },

 

// Subscription and Access Control
    subscriptionTier: {
      type: String,
      enum: ["free", "monthly", "yearly", "lifetime"],
      default: "free",
    },
    subscriptionStatus: {
      type: String,
      enum: ["active", "inactive", "expired", "cancelled"],
      default: "inactive",
    },
    subscriptionExpiry: { type: Date },
    accessLevel: {
      type: String,
      enum: ["basic", "premium", "enterprise"],
      default: "basic",
    },
    downloadCount: { type: Number, default: 0 },
    monthlyDownloadLimit: { type: Number, default: 5 }, // For free users
  },
  { timestamps: true }
);

// Virtual field to fetch the current active subscription
userSchema.virtual("activeSubscription", {
  ref: "Subscription",
  localField: "_id",
  foreignField: "userId",
  justOne: true, // Ensure it only returns the latest subscription
  options: { sort: { endDate: -1 }, match: { isActive: true } },
});

// Method to check if user has access to a product
userSchema.methods.hasAccessToProduct = function(product) {
  // Free products are accessible to everyone
  if (!product.isPaid) return true;

  // Check subscription status
  if (this.subscriptionStatus !== "active") return false;

  // Check access level requirements
  const accessLevels = {
    basic: ["free"],
    premium: ["free", "monthly", "yearly"],
    enterprise: ["free", "monthly", "yearly", "lifetime"]
  };

  const requiredTiers = accessLevels[product.accessLevel] || ["free"];
  return requiredTiers.includes(this.subscriptionTier);
};

// Method to check download limits
userSchema.methods.canDownload = function() {
  if (this.subscriptionTier === "free") {
    return this.downloadCount < this.monthlyDownloadLimit;
  }
  return true; // Paid users have unlimited downloads
};

// Method to increment download count
userSchema.methods.incrementDownload = function() {
  this.downloadCount += 1;
  return this.save();
};

export default mongoose.model("User", userSchema);
