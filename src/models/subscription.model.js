import mongoose from "mongoose";
import { TRANSACTION_STATUSES } from "../utils/constants.js";

const subscriptionSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    planId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Plan",
      required: true,
    },

    // Subscription period
    startDate: { type: Date, required: true, default: Date.now },
    endDate: { type: Date },

    // Status tracking
    isActive: { type: Boolean, default: true },
    isCurrent: { type: Boolean, default: false }, // Indicates the active subscription
    status: {
      type: String,
      enum: ["active", "inactive", "cancelled", "expired", "pending"],
      default: "pending",
    },

    // Renewal settings
    autoRenew: { type: Boolean, default: true },
    nextRenewalDate: { type: Date },

    // Transaction references
    initialTransaction: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Transaction",
    }, // The transaction that created this subscription
    lastTransaction: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Transaction",
    }, // Most recent transaction (renewal, upgrade, etc.)

    // Cancellation details
    cancellation: {
      cancelledAt: Date,
      cancelledBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
      },
      reason: String,
      effectiveDate: Date, // When cancellation takes effect
    },

    // Trial information
    trial: {
      isTrialActive: { type: Boolean, default: false },
      trialStartDate: Date,
      trialEndDate: Date,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes for better performance
subscriptionSchema.index({ userId: 1, isCurrent: 1 });
subscriptionSchema.index({ userId: 1, status: 1 });
subscriptionSchema.index({ endDate: 1, isActive: 1 });
subscriptionSchema.index({ nextRenewalDate: 1, autoRenew: 1 });

// Virtual to check if subscription is expired
subscriptionSchema.virtual('isExpired').get(function() {
  return this.endDate && new Date() > this.endDate;
});

// Virtual to check if subscription needs renewal
subscriptionSchema.virtual('needsRenewal').get(function() {
  return this.autoRenew && this.nextRenewalDate && new Date() >= this.nextRenewalDate;
});

// Virtual to get days until expiration
subscriptionSchema.virtual('daysUntilExpiration').get(function() {
  if (!this.endDate) return null;
  const diffTime = this.endDate - new Date();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

// Middleware to ensure only one active "current" subscription exists per user
subscriptionSchema.pre("save", async function (next) {
  if (this.isCurrent) {
    await mongoose
      .model("Subscription")
      .updateMany(
        { userId: this.userId, isCurrent: true, _id: { $ne: this._id } },
        { isCurrent: false }
      );
  }

  // Auto-set status based on dates and activity
  if (this.isExpired && this.status === 'active') {
    this.status = 'expired';
    this.isActive = false;
  }

  next();
});

// Static method to find active subscription for user
subscriptionSchema.statics.findActiveForUser = function(userId) {
  return this.findOne({
    userId,
    isCurrent: true,
    isActive: true,
    status: 'active',
  }).populate('planId initialTransaction lastTransaction');
};

// Instance method to cancel subscription
subscriptionSchema.methods.cancel = function(cancelledBy, reason, effectiveDate = null) {
  this.cancellation = {
    cancelledAt: new Date(),
    cancelledBy,
    reason,
    effectiveDate: effectiveDate || this.endDate || new Date(),
  };

  // If effective immediately, update status
  if (!effectiveDate || effectiveDate <= new Date()) {
    this.status = 'cancelled';
    this.isActive = false;
    this.autoRenew = false;
  }

  return this.save();
};

// Instance method to renew subscription
subscriptionSchema.methods.renew = function(newEndDate, transaction) {
  this.endDate = newEndDate;
  this.lastTransaction = transaction._id;
  this.status = 'active';
  this.isActive = true;

  // Calculate next renewal date based on plan duration
  if (this.autoRenew) {
    // This would need to be implemented based on plan duration
    // For now, just set it to the end date
    this.nextRenewalDate = newEndDate;
  }

  return this.save();
};

export default mongoose.model("Subscription", subscriptionSchema);
