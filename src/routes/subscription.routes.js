import express from "express";
import {
  createSubscription,
  assignSubscription,
  getSubscriptions,
  getSubscriptionById,
  updateSubscription,
  cancelSubscription,
  deleteSubscription,
} from "../controllers/subscription.controller.js";

import {
  validateCreateSubscription,
  validateAssignSubscription,
  validateSubscriptionId,
  validateUpdateSubscription,
} from "../validators/subscription.validator.js";
import { validateRequest } from "../middlewares/validateRequest.middleware.js";
import { authorize } from "../middlewares/auth.middleware.js";
import { requireAuth } from "@clerk/express";

const router = express.Router();

router.get("/", getSubscriptions); // Get all subscriptions

// Protect all routes below this middleware
router.use(requireAuth());
router.use(authorize(["admin", "super_admin"]));
// Admin Routes
router.get(
  "/:id",

  validateSubscriptionId,
  validateRequest,
  getSubscriptionById
); // Get subscription by ID
router.delete(
  "/:id",
  validateSubscriptionId,
  validateRequest,
  deleteSubscription
); // Delete subscription by ID

// User and Admin Routes
router.post(
  "/create",
  validateCreateSubscription,
  validateRequest,
  createSubscription
); // Create a subscription
router.post(
  "/assign",
  validateAssignSubscription,
  validateRequest,
  assignSubscription
); // Assign a subscription to a user
router.put(
  "/:id",
  validateSubscriptionId,
  validateUpdateSubscription,
  validateRequest,
  updateSubscription
); // Update a subscription by ID
router.post(
  "/:id/cancel",
  validateSubscriptionId,
  validateRequest,
  cancelSubscription
); // Cancel a subscription by ID

export default router;
