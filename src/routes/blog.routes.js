import express from "express";
import {
  createBlog,
  deleteBlog,
  getAllBlogs,
  getBlogById,
  getBlogsWithPagination,
  updateBlog,
} from "../controllers/blog.controller.js";
import { authorize } from "../middlewares/auth.middleware.js";
import { requireAuth } from "@clerk/express";

const router = express.Router();




router.use(requireAuth());
router.use(authorize(["admin", "super_admin"]));



/**
 * @route GET /blogs
 * @desc Get all blogs
 */
router.get("/", getAllBlogs);

/**
 * @route GET /blogs (pagination)
 * @desc Get all blogs with pagination
 */

router.get("/pagination", getBlogsWithPagination);

/**
 * @route GET /blogs/:id
 * @desc Get a blog by ID
 */
router.get("/:id", getBlogById);


/**
 * @route POST /blogs
 * @desc Create a new blog
 */
router.post("/", createBlog);

/**
 * @route PUT /blogs/:id
 * @desc Update a blog
 */
router.put("/:id", updateBlog);

/**
 * @route DELETE /blogs/:id
 * @desc Delete a blog
 */
router.delete("/:id", deleteBlog);

export default router;
