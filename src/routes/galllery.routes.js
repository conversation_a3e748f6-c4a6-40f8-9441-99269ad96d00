// src/routes/gallery.routes.js
import express from "express";
import {
  createGalleryItem,
  getAllGalleryItemsNotDeleted,
  getGalleryItemsWithPagination,
  getGalleryItemsWithPaginationandSearch,
  getGalleryItemById,
  updateGalleryItem,
  deleteGalleryItems,
  permanentDeleteGalleryItems,
  featureGalleryItems,
  restoreGalleryItems,
} from "../controllers/gallery.controller.js";
import { authorize } from "../middlewares/auth.middleware.js";
import { requireAuth } from "@clerk/express";

const router = express.Router();
router.use(requireAuth());
router.use(authorize(["admin", "super_admin"]));

router.post("/create", createGalleryItem);
router.get("/all", getAllGalleryItemsNotDeleted);
router.get("/pagination", getGalleryItemsWithPagination);
router.get("/pagination/search", getGalleryItemsWithPaginationandSearch);
router.get("/:id", getGalleryItemById);
router.put("/:id", updateGalleryItem);
router.delete("/delete", deleteGalleryItems);
router.delete("/permanent-delete", permanentDeleteGalleryItems);
router.post("/feature", featureGalleryItems);
router.post("/restore", restoreGalleryItems);

export default router;
