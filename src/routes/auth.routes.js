// authRoutes.js
import express from "express";
import {
  getDashboarAccess,
  getUserByClerkId,
} from "../controllers/auth.controller.js";
import { createUserValidator } from "../validators/user.validator.js";
import { validateRequest } from "../middlewares/validateRequest.middleware.js";
import { createUser } from "../controllers/user.controller.js";

const router = express.Router();

router.get("/dashboard/:id", getDashboarAccess);
router.get("/clerk/:id", getUserByClerkId);

// Public Routes
router.post("/register", createUserValidator, validateRequest, createUser); // Register new user
// router.post("/login", loginUser); // Login user

export default router;
