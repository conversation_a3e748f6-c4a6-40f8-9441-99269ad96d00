// Test script for the subscription system
import mongoose from "mongoose";
import User from "./src/models/user.model.js";
import Plan from "./src/models/plan.model.js";
import Transaction from "./src/models/transaction.model.js";
import { subscriptionService } from "./src/services/subscription/SubscriptionService.js";
import { razorpayWebhookHandler } from "./src/services/webhooks/RazorpayWebhookHandler.js";
import { initializeWebhookHandlers } from "./src/services/webhooks/index.js";

// Test configuration
const MONGODB_URI = process.env.MONGODB_URI || "mongodb://localhost:27017/designbyte-test";

async function testSubscriptionSystem() {
  try {
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI);
    console.log("✅ Connected to MongoDB");

    // Initialize webhook handlers
    initializeWebhookHandlers();

    // Mock webhook secret for testing
    process.env.RAZORPAY_WEBHOOK_SECRET = "test-webhook-secret";

    // Clean up test data
    await User.deleteMany({ email: { $regex: /test.*@example\.com/ } });
    await Plan.deleteMany({ name: { $regex: /Test.*/ } });
    await Transaction.deleteMany({});

    // Create test plan
    console.log("\n🧪 Creating test plan...");
    const testPlan = new Plan({
      name: "Test Pro Plan",
      description: "Test plan for subscription system",
      price: 29.99,
      currency: "USD",
      duration: "monthly",
      benefits: "Access to premium templates and features",
    });
    await testPlan.save();
    console.log("✅ Test plan created:", testPlan.name);

    // Test 1: Razorpay subscription activation webhook
    console.log("\n🧪 Test 1: Razorpay subscription activation...");
    const razorpayActivationPayload = {
      event: "subscription.activated",
      payload: {
        subscription: {
          id: "sub_test123",
          status: "active",
          plan_id: "plan_test123",
          customer_id: "cust_test123",
          start_at: Math.floor(Date.now() / 1000),
          end_at: Math.floor((Date.now() + 30 * 24 * 60 * 60 * 1000) / 1000), // 30 days
          current_start: Math.floor(Date.now() / 1000),
          current_end: Math.floor((Date.now() + 30 * 24 * 60 * 60 * 1000) / 1000),
          plan: {
            item: {
              name: "Test Pro Plan",
              amount: 2999, // in paise
              currency: "INR"
            }
          },
          notes: {
            email: "<EMAIL>",
            customer_name: "Test User"
          }
        }
      }
    };

    // Mock signature verification for testing
    razorpayWebhookHandler.verifySignature = () => true;

    const webhookData1 = await razorpayWebhookHandler.processWebhook(
      razorpayActivationPayload,
      "test-signature"
    );
    
    const result1 = await subscriptionService.handleSubscriptionWebhook(webhookData1);
    console.log("✅ Subscription activation result:", result1);

    // Verify user was created and subscription activated
    const user1 = await User.findOne({ email: "<EMAIL>" });
    if (user1) {
      console.log("✅ User created:", {
        email: user1.email,
        isPro: user1.isPro,
        subscriptionTier: user1.subscriptionTier,
        subscriptionStatus: user1.subscriptionStatus,
      });
    } else {
      console.log("❌ User not found - webhook processing may have failed");
      return;
    }

    // Test 2: Payment success webhook
    console.log("\n🧪 Test 2: Razorpay payment success...");
    const razorpayPaymentPayload = {
      event: "payment.captured",
      payload: {
        payment: {
          id: "pay_test123",
          amount: 2999,
          currency: "INR",
          status: "captured",
          method: "card",
          email: "<EMAIL>"
        }
      }
    };

    const webhookData2 = await razorpayWebhookHandler.processWebhook(
      razorpayPaymentPayload,
      "test-signature"
    );
    
    const result2 = await subscriptionService.handleSubscriptionWebhook(webhookData2);
    console.log("✅ Payment success result:", result2);

    // Test 3: Subscription cancellation webhook
    console.log("\n🧪 Test 3: Razorpay subscription cancellation...");
    const razorpayCancellationPayload = {
      event: "subscription.cancelled",
      payload: {
        subscription: {
          id: "sub_test123",
          status: "cancelled",
          customer_id: "cust_test123",
          notes: {
            email: "<EMAIL>"
          }
        }
      }
    };

    const webhookData3 = await razorpayWebhookHandler.processWebhook(
      razorpayCancellationPayload,
      "test-signature"
    );
    
    const result3 = await subscriptionService.handleSubscriptionWebhook(webhookData3);
    console.log("✅ Subscription cancellation result:", result3);

    // Verify subscription was cancelled
    const user3 = await User.findOne({ email: "<EMAIL>" });
    console.log("✅ User after cancellation:", {
      subscriptionStatus: user3.subscriptionStatus,
      cancellation: user3.cancellation,
    });

    // Test 4: Test user subscription methods
    console.log("\n🧪 Test 4: User subscription methods...");
    
    // Create a new user for testing methods
    const uniqueId = Date.now();
    const testUser = new User({
      email: `test-methods-${uniqueId}@example.com`,
      username: `testmethods${uniqueId}`,
      firstName: "Test",
      lastName: "Methods",
      clerkId: `test_clerk_${uniqueId}`, // Unique clerkId
      googleId: `test_google_${uniqueId}`, // Unique googleId
    });
    await testUser.save();

    console.log("Before activation:");
    console.log("- Has active subscription:", testUser.hasActiveSubscription());
    console.log("- Is lifetime:", testUser.isLifetimePro);
    console.log("- Can download:", testUser.canDownload());

    // Activate subscription
    await testUser.activateSubscription(testPlan, null);
    console.log("\nAfter activation:");
    console.log("- Has active subscription:", testUser.hasActiveSubscription());
    console.log("- Subscription tier:", testUser.subscriptionTier);
    console.log("- Can download:", testUser.canDownload());

    // Test lifetime subscription
    const lifetimePlan = new Plan({
      name: "Test Lifetime Plan",
      price: 299.99,
      duration: "lifetime",
      benefits: "Lifetime access to all features",
    });
    await lifetimePlan.save();

    await testUser.activateSubscription(lifetimePlan, null);
    console.log("\nAfter lifetime activation:");
    console.log("- Is lifetime:", testUser.isLifetimePro);
    console.log("- Subscription tier:", testUser.subscriptionTier);
    console.log("- End date:", testUser.subscriptionEndDate);

    // Test 5: Transaction creation
    console.log("\n🧪 Test 5: Transaction creation...");
    const transactions = await Transaction.find({});
    console.log("✅ Total transactions created:", transactions.length);
    
    if (transactions.length > 0) {
      const lastTransaction = transactions[transactions.length - 1];
      console.log("Last transaction:", {
        type: lastTransaction.type,
        amount: lastTransaction.amount,
        status: lastTransaction.status,
        gateway: lastTransaction.paymentGateway,
        userSubscriptionData: lastTransaction.userSubscriptionData,
      });
    }

    console.log("\n🎉 All subscription system tests completed successfully!");
    console.log("\n📋 Test Summary:");
    console.log("✅ Webhook handler registration");
    console.log("✅ Razorpay webhook processing");
    console.log("✅ User creation from webhook");
    console.log("✅ Subscription activation");
    console.log("✅ Payment processing");
    console.log("✅ Subscription cancellation");
    console.log("✅ User subscription methods");
    console.log("✅ Transaction creation");
    console.log("✅ Lifetime subscription handling");

    console.log("\n🚀 Subscription system is ready for production!");
    console.log("\n📝 Next steps:");
    console.log("1. Configure webhook URLs in Razorpay dashboard");
    console.log("2. Set up webhook secrets in environment variables");
    console.log("3. Test with real Razorpay webhooks");
    console.log("4. Add other payment gateways as needed");

  } catch (error) {
    console.error("❌ Test failed:", error.message);
    console.error(error.stack);
  } finally {
    // Close MongoDB connection
    await mongoose.connection.close();
    console.log("🔌 Disconnected from MongoDB");
  }
}

// Run tests
testSubscriptionSystem();
